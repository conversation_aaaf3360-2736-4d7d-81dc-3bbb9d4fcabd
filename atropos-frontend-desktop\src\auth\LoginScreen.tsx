// atropos-frontend-desktop/src/auth/LoginScreen.tsx
import React, { useState } from 'react';
import {
  Box,
  Input,
  Button,
  Text,
  Stack,
  FormControl,
  FormLabel,
  Avatar,
  useToast,
  Spinner,
  Heading,
  Center,
  VStack,
} from '@chakra-ui/react';

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

interface LoginScreenProps {
  onLoginSuccess: (token: string, user: User) => void;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ onLoginSuccess }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);

    if (!username || !password) {
      toast({
        title: '<PERSON><PERSON><PERSON>',
        description: '<PERSON>llanıcı adı ve şifre bo<PERSON> bırakılamaz.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Giriş başarısız oldu.');
      }

      const data = await response.json();
      const profileResponse = await fetch(`${API_URL}/auth/profile`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${data.access_token}`,
        },
      });
      if (!profileResponse.ok) {
        throw new Error('Kullanıcı profil bilgileri çekilemedi.');
      }
      const userData: User = await profileResponse.json();

      toast({
        title: 'Giriş Başarılı',
        description: 'Sisteme yönlendiriliyorsunuz.',
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
      onLoginSuccess(data.access_token, userData);
    } catch (err: any) {
      toast({
        title: 'Giriş Başarısız',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
      console.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Center minH="100vh" w="100vw" bg="gray.100">
      <Box
        p={8}
        borderWidth="1px"
        borderRadius="lg" // Temadan gelecek
        boxShadow="lg"   // Temadan gelecek
        bg="white"
        textAlign="center"
        width="400px"
        maxW="md"
      >
        <VStack spacing={6} mb={8}>
          <Avatar size="xl" name={username || 'Kullanıcı'} />
          <Heading as="h2" size="lg" color="gray.800">
            {username || 'Kullanıcı Adı'}
          </Heading>
          <Text fontSize="md" color="gray.500">Lütfen şifrenizi girin.</Text>
        </VStack>

        <form onSubmit={handleLogin}>
          <VStack spacing={4}>
            <FormControl id="username" isRequired>
              <FormLabel>Kullanıcı Adı</FormLabel>
              <Input
                type="text"
                placeholder="Kullanıcı adınız"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                isDisabled={loading}
                variant="outline"
              />
            </FormControl>

            <FormControl id="password" isRequired>
              <FormLabel>Şifre</FormLabel>
              <Input
                type="password"
                placeholder="Şifreniz"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                isDisabled={loading}
                variant="outline"
              />
            </FormControl>

            <Button
              type="submit"
              colorScheme="brand"
              isLoading={loading}
              loadingText="Giriş Yapılıyor..."
              width="full"
              size="lg"
              mt={4}
              isDisabled={!username || !password}
            >
              Giriş Yap
            </Button>
          </VStack>
        </form>
      </Box>
    </Center>
  );
};

export default LoginScreen;
