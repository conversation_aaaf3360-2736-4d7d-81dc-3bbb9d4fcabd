// atropos-frontend-desktop/src/auth/LoginScreen.tsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Input,
  Button,
  Text,
  Stack,
  FormControl,
  FormLabel,
  Avatar,
  useToast,
  Spinner,
  Select,
  Heading,
  VStack,
  Center,
  Flex,
} from '@chakra-ui/react';
import { CheckCircleIcon, WarningIcon, CloseIcon } from '@chakra-ui/icons'; // Chakra UI ikonlarını import et

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

interface LoginScreenProps {
  onLoginSuccess: (token: string) => void;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ onLoginSuccess }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useToast();

  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(true);
  const [usersError, setUsersError] = useState<string | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  useEffect(() => {
    const fetchUsers = async () => {
      setUsersLoading(true);
      setUsersError(null);
      try {
        const response = await fetch(`${API_URL}/user`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Kullanıcılar yüklenemedi!');
        }
        const data: User[] = await response.json();
        setAllUsers(data);
        if (data.length > 0) {
          setSelectedUserId(data[0].id); // İlk kullanıcıyı varsayılan olarak seç
          setUsername(data[0].username);
        }
      } catch (err: any) {
        setUsersError(err.message);
        toast({
          title: 'Kullanıcılar yüklenemedi.',
          description: err.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
          position: 'top',
        });
      } finally {
        setUsersLoading(false);
      }
    };
    fetchUsers();
  }, []);

  useEffect(() => {
    if (selectedUserId) {
      const selectedUser = allUsers.find((user) => user.id === selectedUserId);
      if (selectedUser) {
        setUsername(selectedUser.username);
      }
    } else {
      setUsername('');
    }
  }, [selectedUserId, allUsers]);

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);

    if (!username || !password) {
      toast({
        title: 'Giriş Hatası',
        description: 'Kullanıcı adı ve şifre boş bırakılamaz.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Giriş başarısız oldu.');
      }

      const data = await response.json();
      toast({
        title: 'Giriş Başarılı',
        description: 'Sisteme yönlendiriliyorsunuz.',
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
      onLoginSuccess(data.access_token);
    } catch (err: any) {
      toast({
        title: 'Giriş Başarısız',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
      console.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  const currentUser = selectedUserId ? allUsers.find((u) => u.id === selectedUserId) : null;

  return (
    <Box
      p={8}
      borderWidth="1px"
      borderRadius="xl" // Temadaki xl değeri
      boxShadow="2xl" // Temadaki 2xl değeri
      bg="neutral.50" // Temadaki nötr arka plan rengi
      textAlign="center"
      width="400px"
      maxW="md"
    >
      <VStack spacing={6} mb={8}>
        <Avatar size="xl" name={currentUser?.firstName + ' ' + currentUser?.lastName || 'Kullanıcı'} />
        <Heading as="h2" size="lg" color="neutral.900">
          {currentUser?.firstName + ' ' + currentUser?.lastName || 'Kullanıcı Adı'}
        </Heading>
        <Text fontSize="md" color="neutral.700">Lütfen şifrenizi girin.</Text>
      </VStack>

      <form onSubmit={handleLogin}>
        <VStack spacing={4}>
          <FormControl id="user-select" isRequired>
            <FormLabel color="neutral.700">Kullanıcı Seç</FormLabel>
            {usersLoading ? (
              <Spinner size="sm" label="Kullanıcılar yükleniyor..." />
            ) : usersError ? (
              <Text color="error.500">{usersError}</Text>
            ) : (
              <Select
                placeholder="Kullanıcı Seçin"
                value={selectedUserId || ''}
                onChange={(e) => setSelectedUserId(e.target.value)}
                isDisabled={usersLoading}
                variant="outline"
              >
                {allUsers.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.firstName} {user.lastName} ({user.username})
                  </option>
                ))}
              </Select>
            )}
          </FormControl>

          <FormControl id="password" isRequired>
            <FormLabel color="neutral.700">Şifre</FormLabel>
            <Input
              type="password"
              placeholder="Şifreniz"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              isDisabled={loading || usersLoading}
              variant="outline"
            />
          </FormControl>

          <Button
            type="submit"
            colorScheme="brand" // Temadaki ana marka rengi
            isLoading={loading}
            loadingText="Giriş Yapılıyor..."
            width="full"
            size="lg"
            mt={6}
            isDisabled={!username || !password || usersLoading}
          >
            Giriş Yap
          </Button>
        </VStack>
      </form>
      {/* Sol alttaki kullanıcı seçimi ve sağ alttaki sistem ikonları için yer tutucular */}
      {/* Bunlar App.tsx içinde overlay veya sabit konumlu bileşenler olarak eklenecek */}
    </Box>
  );
};

export default LoginScreen;
