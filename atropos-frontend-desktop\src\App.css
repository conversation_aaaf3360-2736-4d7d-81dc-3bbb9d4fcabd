/* atropos-frontend-desktop/src/App.css */
/* Bu dosyadaki tüm custom stil sınıflarını kaldırın (Chakra UI yönetecek) */

body {
  margin: 0;
  /* Font ailesi ve arka plan rengi theme/index.ts'deki global stilden gelecek */
  /* Chakra UI kendi sıfırlama stillerini uygular */
}

#root {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  /* Arka plan rengi theme/index.ts'deki global stilden gelecek */
  /* Windows 11 arka plan resmini burada global olarak ayarlayalım */
  background-image: url('/windows-11-bg.jpg'); /* public klasörünüzdeki resminizin doğru yolu */
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden; /* Taşmayı engelle */
}

.app-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  /* Windows 11 pencere gibi görünmesi için */
  backdrop-filter: blur(30px) saturate(180%); /* Bulanıklık efekti (Electron destekliyorsa) */
  background-color: var(--chakra-colors-acrylic-light); /* Yarı saydam arka plan */
  border-radius: var(--chakra-radii-xl); /* Köşe yuvarlaklığı */
  box-shadow: var(--chakra-shadows-xl); /* Gölge */
  margin: 20px; /* Ekran kenarlarından boşluk */
}
