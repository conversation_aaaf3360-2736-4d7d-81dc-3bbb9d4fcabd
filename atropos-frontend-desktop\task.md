<PERSON><PERSON>, harika bir ba<PERSON><PERSON><PERSON><PERSON> noktası\! Chakra UI v2.8 ile basit bir Login sayfası ve Dashboard sayfas<PERSON> yapmak, projenin UI/UX temelini sağlam atmak için mükemmel bir adım.

**Chakra UI v2.8**'i özellikle not ettim. Bu sürümle uyumlu olacak şekilde ilerleyeceğiz.

### **Adım 47: Chakra UI v2.8 Kurulumu ve Temel Yapılandırma**

İlk olarak, mevcut projemize Chakra UI'ı entegre etmemiz gerekiyor.

**Yapılacaklar:**

1.  **Chakra UI ve Bağımlılıklarını Kurun:**
    `atropos-frontend-desktop` dizininizde terminali açın ve aşağıdaki komutları çalıştırın:

    ```bash
    npm install @chakra-ui/react@2.8 @emotion/react@11 @emotion/styled@11 framer-motion@6
    ```

      * <PERSON><PERSON> komut, Chakra UI v2.8'i ve onun gerekt<PERSON> `@emotion/react`, `@emotion/styled` ve `framer-motion` bağımlılıklarını kuracaktır.

2.  **Chakra UI Sağlayıcısını Uygulamaya Entegre Edin:**
    `atropos-frontend-desktop/src/main.tsx` dosyasını açın ve içeriğini aşağıdaki gibi güncelleyin. Bu, Chakra UI'ın stil sistemini tüm uygulamamız için kullanılabilir hale getirecektir.

    ```typescript
    // atropos-frontend-desktop/src/main.tsx
    import React from 'react';
    import { createRoot } from 'react-dom/client';
    import { ChakraProvider } from '@chakra-ui/react'; // ChakraProvider'ı import et
    import './App.css'; // Genel stilleri import et
    import App from './App'; // App.tsx'i import et

    createRoot(document.getElementById('root')!).render(
      <React.StrictMode>
        <ChakraProvider> {/* Uygulamayı ChakraProvider ile sarmala */}
          <App />
        </ChakraProvider>
      </React.StrictMode>
    );
    ```

      * **Not:** Eğer daha önce burada başka bir UI kütüphanesinin sağlayıcısı (örn. FluentProvider) varsa, onu kaldırın.

3.  **`atropos-frontend-desktop/src/App.css` Dosyasını Güncelleyin (Minimal CSS):**
    Chakra UI kendi sıfırlama ve temel stillerini getirdiği için, `App.css` dosyasındaki karmaşık CSS'i kaldırıp sadece gerekli genel stilleri bırakıyoruz.

    ```css
    /* atropos-frontend-desktop/src/App.css */
    /* Bu dosyadaki tüm custom stil sınıflarını (login-container, input-group vb.) kaldırın */
    /* Chakra UI kendi stillerini yönetecektir */

    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
        "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
        sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    #root {
      width: 100%;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f0f2f5; /* Hafif gri bir arka plan */
    }

    .app-container {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    ```

Bu adımları tamamladığında bana haber ver. Ardından Login ekranını Chakra UI bileşenleriyle yeniden oluşturmaya başlayacağız.