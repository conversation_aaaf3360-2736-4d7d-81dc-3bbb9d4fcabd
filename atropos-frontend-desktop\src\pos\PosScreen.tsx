// atropos-frontend-desktop/src/pos/PosScreen.tsx
import React from 'react';
import { Box, Text, Heading, VStack } from '@chakra-ui/react';

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
}

interface PosScreenProps {
  currentUser: User;
}

const PosScreen: React.FC<PosScreenProps> = ({ currentUser }) => {
  return (
    <VStack spacing={6} align="stretch">
      <Heading as="h2" size="lg" color="gray.800">
        POS Ekranı
      </Heading>
      <Text color="gray.600">
        <PERSON><PERSON> geldiniz, {currentUser.firstName} {currentUser.lastName}!
      </Text>
      <Box
        p={6}
        borderRadius="lg"
        bg="white"
        borderWidth="1px"
        borderColor="gray.200"
        boxShadow="md"
      >
        <Text color="gray.600">
          POS işlemleri burada görüntülenecek...
        </Text>
      </Box>
    </VStack>
  );
};

export default PosScreen;
