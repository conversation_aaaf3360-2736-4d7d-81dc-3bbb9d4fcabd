// atropos-frontend-desktop/src/pos/PosScreen.tsx
import React from 'react';
import { Box, Text, Heading, VStack } from '@chakra-ui/react';

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
}

interface PosScreenProps {
  currentUser: User;
}

const PosScreen: React.FC<PosScreenProps> = ({ currentUser }) => {
  return (
    <VStack spacing={6} align="stretch">
      <Heading as="h2" size="lg" color="neutral.900">
        POS Ekranı
      </Heading>
      <Text color="neutral.700">
        <PERSON><PERSON> geldiniz, {currentUser.firstName} {currentUser.lastName}!
      </Text>
      <Box
        p={6}
        borderRadius="lg"
        bg="neutral.100"
        borderWidth="1px"
        borderColor="neutral.200"
      >
        <Text color="neutral.600">
          POS işlemleri burada görüntülenecek...
        </Text>
      </Box>
    </VStack>
  );
};

export default PosScreen;
