// atropos-frontend-desktop/src/theme/index.ts
import { extendTheme, type ThemeConfig } from '@chakra-ui/react'; // type ThemeConfig'i import et

// Windows 11'in temel renklerinden esinlenerek bir palet tanımlayalım
// Microsoft Fluent Design renk yönergelerinden alınmıştır
const colors = {
  // Marka rengi (Windows 11 mavisi)
  brand: {
    50: '#E6F0FA',
    100: '#BFDBF7',
    200: '#94C0EE',
    300: '#69A4E5',
    400: '#408ADF',
    500: '#0078D4', // Windows 11 aksan rengi
    600: '#0060B2',
    700: '#004880',
    800: '#003053',
    900: '#00182C',
  },
  // Nötr renkler (Windows 11 arayüz elemanları için gri tonları)
  neutral: {
    50: '#F3F3F3', // Çok açık gri
    100: '#EBEBEB',
    200: '#D6D6D6',
    300: '#BFBFBF',
    400: '#A3A3A3',
    500: '#878787', // Orta gri
    600: '#666666',
    700: '#4D4D4D',
    800: '#333333',
    900: '#1A1A1A', // Koyu gri/siyah
  },
  // Hata, Başarı vb. durum renkleri
  error: {
    500: '#DC3545', // Kırmızı
  },
  success: {
    500: '#28A745', // Yeşil
  },
  warning: {
    500: '#FFC107', // Sarı
  },
};

// Temanın genel konfigürasyonu
const config: ThemeConfig = {
  initialColorMode: 'light', // Varsayılan: Açık tema
  useSystemColorMode: false, // Sistemin temasını takip etme
};

// Genel stiller (body, html vb.)
const styles = {
  global: {
    body: {
      bg: 'transparent', // Arka plan resminin görünmesi için şeffaf
      color: 'neutral.900', // Genel metin rengi (koyu gri)
      fontFamily: `'Segoe UI', 'Helvetica Neue', Arial, sans-serif`, // Windows fontu
    },
    // Mica veya Acrylic efekti için genel CSS değişkenleri
    // Bunları bileşenlerde backdropFilter ve rgba renklerle simüle edeceğiz
    ':root': {
      '--chakra-colors-mica-light': 'rgba(243, 243, 243, 0.8)', // Hafif yarı saydam beyaz
      '--chakra-colors-acrylic-light': 'rgba(255, 255, 255, 0.7)', // Daha belirgin yarı saydam beyaz
    },
  },
};

// Bileşenlerin temel ve varyant stilleri
const components = {
  Button: {
    baseStyle: {
      borderRadius: 'md', // Hafif yuvarlak köşeler
      _focus: { boxShadow: 'none' }, // Odaklandığında dış kenarlık yok
    },
    variants: {
      solid: (props: any) => ({
        bg: props.colorScheme === 'brand' ? 'brand.500' : undefined,
        color: props.colorScheme === 'brand' ? 'white' : 'neutral.900',
        _hover: {
          bg: props.colorScheme === 'brand' ? 'brand.600' : undefined,
        },
        _active: {
          bg: props.colorScheme === 'brand' ? 'brand.700' : undefined,
        },
      }),
      ghost: (props: any) => ({
        color: props.colorScheme === 'brand' ? 'brand.500' : 'neutral.900',
        _hover: {
          bg: props.colorScheme === 'brand' ? 'brand.50' : 'neutral.100',
        },
      }),
    },
  },
  Input: {
    baseStyle: {
      borderRadius: 'md',
      _focus: {
        borderColor: 'brand.500',
        boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
      },
    },
    variants: {
      outline: {
        field: {
          borderColor: 'neutral.200', // Varsayılan kenarlık
          _hover: {
            borderColor: 'neutral.300',
          },
        },
      },
    },
  },
  Select: {
    baseStyle: {
      borderRadius: 'md',
      _focus: {
        borderColor: 'brand.500',
        boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
      },
    },
    variants: {
      outline: {
        field: {
          borderColor: 'neutral.200',
          _hover: {
            borderColor: 'neutral.300',
          },
        },
      },
    },
  },
  Text: {
    baseStyle: {
      fontFamily: `'Segoe UI', 'Helvetica Neue', Arial, sans-serif`,
    },
  },
  Heading: {
    baseStyle: {
      fontFamily: `'Segoe UI', 'Helvetica Neue', Arial, sans-serif`,
      fontWeight: 'semibold', // Başlıklar için varsayılan ağırlık
    },
  },
  // Kart benzeri kutular için genel stil
  Card: { // Bu, Chakra'daki Box bileşenini Card gibi kullanacağımız zaman işe yarar
    baseStyle: {
      borderRadius: 'xl', // Belirgin yuvarlaklık
      boxShadow: 'xl',    // Belirgin gölge
      bg: 'neutral.50', // Kartların varsayılan arka planı
    },
  },
};

// Gölgeler (Windows 11'in daha yumuşak gölgeleri için)
const shadows = {
  sm: '0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.16)',
  md: '0 4px 6px rgba(0, 0, 0, 0.1), 0 3px 5px rgba(0, 0, 0, 0.15)',
  lg: '0 10px 15px rgba(0, 0, 0, 0.15), 0 4px 6px rgba(0, 0, 0, 0.1)',
  xl: '0 20px 25px rgba(0, 0, 0, 0.2), 0 10px 10px rgba(0, 0, 0, 0.15)', // Çok belirgin
  '2xl': '0 25px 50px rgba(0, 0, 0, 0.25)',
};

// Yuvarlak köşeler (Windows 11'e benzer)
const radii = {
  none: '0',
  sm: '0.125rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem', // Özellikle kartlar ve paneller için
  '2xl': '1rem',
  '3xl': '1.5rem',
  full: '9999px',
};

// Temayı genişlet
const theme = extendTheme({
  config,
  styles,
  colors,
  shadows,
  radii,
  components,
  // Fontları da buraya ekleyebiliriz
  // fonts: {
  //   heading: `'Segoe UI', sans-serif`,
  //   body: `'Segoe UI', sans-serif`,
  // },
});

export default theme;
