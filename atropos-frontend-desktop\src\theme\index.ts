// atropos-frontend-desktop/src/theme/index.ts
import { extendTheme } from '@chakra-ui/react';

// Sadece birkaç temel rengi tanımlayalım, diğerlerini Chakra'nın varsayılanına bırakalım
const colors = {
  brand: {
    500: '#3182CE', // Chakra'nın varsayılan mavi tonu (Örn: Giri<PERSON> butonu için)
  },
  // Metinler için gri tonlarını belirginleştirelim
  gray: {
    800: '#2D3748', // Koyu metin
    100: '#F7FAFC', // Açık arka plan
  },
  red: { // Hata mesajları için
    500: '#E53E3E',
  },
  green: { // Başarı mesajları için
    500: '#38A169',
  },
};

const config = {
  initialColorMode: 'light',
  useSystemColorMode: false,
};

const styles = {
  global: {
    body: {
      bg: 'gray.100', // Uygulamanın genel arka planı
      color: 'gray.800', // Uygulamanın genel metin rengi
    },
  },
};

const theme = extendTheme({ colors, config, styles });

export default theme;
