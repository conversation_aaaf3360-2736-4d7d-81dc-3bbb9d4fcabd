// atropos-frontend-desktop/src/dashboard/MainDashboard.tsx
import React from 'react';
import { Box, Button, Text, Heading, Flex, Spacer, useToast, VStack, HStack, Avatar, Menu, MenuButton, MenuList, MenuItem, Divider } from '@chakra-ui/react';
import PosScreen from '../pos/PosScreen'; // PosScreen bileşenini import et

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
  avatar?: string;
}

interface MainDashboardProps {
  onLogout: () => void;
  user: User;
}

const MainDashboard: React.FC<MainDashboardProps> = ({ onLogout, user }) => {
  const toast = useToast();

  return (
    <Flex direction="column" height="100%" width="100%" borderRadius="xl" boxShadow="2xl" bg="neutral.50"> {/* Ana uygulama penceresi gibi */}
      {/* Header (Başlık Çubuğu) */}
      <Flex
        bg="brand.500" // Temadaki marka rengi
        color="white"
        p={3} // Daha az padding
        align="center"
        boxShadow="md"
        borderTopRadius="xl" // Üst köşeleri yuvarlak
        borderBottom="1px solid" borderColor="brand.600" // Alt kenarlık
      >
        <Heading as="h1" size="md" ml={2}>Atropos POS</Heading>
        <Text fontSize="sm" ml={4}>v1.0</Text>
        <Spacer />
        
        {/* Kullanıcı Menüsü */}
        <Menu>
          <MenuButton as={Button} rightIcon={<Text ml={1} fontSize="lg">▼</Text>} variant="ghost" color="white" _hover={{ bg: "brand.600" }}>
            <Flex align="center">
              <Avatar size="sm" name={user.firstName + ' ' + user.lastName} mr={2} bg="brand.700" color="white" />
              <Text>{user.firstName} {user.lastName}</Text>
            </Flex>
          </MenuButton>
          <MenuList bg="white" borderColor="gray.200" boxShadow="lg" borderRadius="md"> {/* Menü arka planı ve gölgesi */}
            <MenuItem onClick={() => toast({ title: "Profil", description: "Profil sayfasına git.", status: "info" })} color="neutral.800">Profilim</MenuItem>
            <MenuItem onClick={() => toast({ title: "Ayarlar", description: "Ayarlar sayfasına git.", status: "info" })} color="neutral.800">Ayarlar</MenuItem>
            <Divider />
            <MenuItem onClick={onLogout} color="red.500">Çıkış Yap</MenuItem>
          </MenuList>
        </Menu>
      </Flex>

      {/* Main Content Area */}
      <Flex flex="1" overflow="hidden"> {/* İçeriğin taşmasını engelle */}
        {/* Sidebar (Sol Menü) */}
        <VStack
          w="200px" // Sabit genişlik
          bg="neutral.100" // Sidebar arka planı
          p={4}
          spacing={4}
          align="stretch"
          boxShadow="sm" // Hafif gölge
          borderRight="1px solid" borderColor="neutral.200"
        >
          <Button variant="ghost" colorScheme="brand" justifyContent="flex-start">Ana Sayfa</Button>
          <Button variant="ghost" colorScheme="brand" justifyContent="flex-start">Siparişler</Button>
          <Button variant="ghost" colorScheme="brand" justifyContent="flex-start">Masalar</Button>
          <Button variant="ghost" colorScheme="brand" justifyContent="flex-start">Ürünler</Button>
          {/* Diğer menü öğeleri buraya eklenecek */}
        </VStack>

        {/* Content Area */}
        <Box flex="1" p={4} overflowY="auto"> {/* İçerik alanı scroll olabilir */}
          <PosScreen currentUser={user} /> {/* POS Ekranını render et */}
        </Box>
      </Flex>

      {/* Footer */}
      <Flex bg="neutral.200" color="neutral.700" p={2} justify="flex-end" fontSize="xs" boxShadow="md" borderBottomRadius="xl"> {/* Alt köşeleri yuvarlak */}
        <Text>&copy; {new Date().getFullYear()} Atropos POS</Text>
      </Flex>
    </Flex>
  );
};

export default MainDashboard;
