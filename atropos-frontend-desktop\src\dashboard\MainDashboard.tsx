// atropos-frontend-desktop/src/dashboard/MainDashboard.tsx
import React from 'react';
import { Box, Button, Text, Heading, Flex, Spacer, useToast, Avatar, Menu, MenuButton, MenuList, MenuItem } from '@chakra-ui/react';
import PosScreen from '../pos/PosScreen'; // PosScreen bileşenini import et (henüz oluşturmadık)

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
  avatar?: string;
}

interface MainDashboardProps {
  onLogout: () => void;
  user: User; // <PERSON><PERSON><PERSON> ya<PERSON> kullanıcı bilgisi
}

const MainDashboard: React.FC<MainDashboardProps> = ({ onLogout, user }) => {
  const toast = useToast();

  return (
    <Flex direction="column" height="100vh" width="100vw">
      {/* Header */}
      <Flex bg="brand.500" color="white" p={4} align="center" boxShadow="md">
        <Heading as="h1" size="md" mr={4}>Atropos POS</Heading>
        <Text fontSize="sm" mr={8}>v1.0</Text>
        <Spacer />

        {/* Kullanıcı Menüsü */}
        <Menu>
          <MenuButton as={Button} rightIcon={<Text fontSize="sm">▼</Text>} variant="ghost" color="white" _hover={{ bg: "brand.600" }}>
            <Flex align="center">
              <Avatar size="sm" name={user.firstName + ' ' + user.lastName} mr={2} bg="brand.100" color="brand.700" />
              <Text>{user.firstName} {user.lastName}</Text>
            </Flex>
          </MenuButton>
          <MenuList color="gray.800" bg="white" boxShadow="md" borderRadius="md"> {/* Menü arka planı ve gölgesi */}
            <MenuItem onClick={() => toast({ title: "Profil", description: "Profil sayfasına git.", status: "info" })} >Profilim</MenuItem>
            <MenuItem onClick={() => toast({ title: "Ayarlar", description: "Ayarlar sayfasına git.", status: "info" })}>Ayarlar</MenuItem>
            <MenuItem onClick={onLogout} color="red.500">Çıkış Yap</MenuItem>
          </MenuList>
        </Menu>
      </Flex>

      {/* Main Content Area - POS Screen */}
      <Box flex="1" p={4} bg="gray.100">
        {/* Şimdilik sadece PosScreen'i direkt render ediyoruz */}
        <PosScreen currentUser={user} />
      </Box>

      {/* Footer */}
      <Flex bg="gray.200" color="gray.600" p={2} justify="flex-end" fontSize="xs" boxShadow="md">
        <Text>&copy; {new Date().getFullYear()} Atropos POS</Text>
      </Flex>
    </Flex>
  );
};

export default MainDashboard;
