// atropos-frontend-desktop/src/main.tsx
import React from 'react';
import { createRoot } from 'react-dom/client';
import { ChakraProvider } from '@chakra-ui/react'; // ChakraProvider'ı import et
import './App.css'; // Genel stilleri import et
import App from './App'; // App.tsx'i import et

createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ChakraProvider> {/* Uygulamayı ChakraProvider ile sarmala */}
      <App />
    </ChakraProvider>
  </React.StrictMode>
);
