// src/App.tsx
import React, { useState } from 'react';
import { Box } from '@chakra-ui/react';
import LoginScreen from './auth/LoginScreen';
import MainDashboard from './dashboard/MainDashboard';
import './App.css';

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
}

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authToken, setAuthToken] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  const handleLoginSuccess = async (token: string) => {
    setAuthToken(token);

    // Token ile kullanıcı bilgilerini al
    try {
      const response = await fetch(`${API_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const userData = await response.json();
        setCurrentUser(userData);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setAuthToken(null);
    setCurrentUser(null);
  };

  return (
    <Box className="app-container">
      {!isAuthenticated ? (
        <LoginScreen onLoginSuccess={handleLoginSuccess} />
      ) : (
        currentUser && (
          <MainDashboard onLogout={handleLogout} user={currentUser} />
        )
      )}
    </Box>
  );
}

export default App;
